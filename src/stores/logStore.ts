import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { OperationLog } from '@/types'

export const useLogStore = defineStore('log', () => {
  // 状态
  const logs = ref<OperationLog[]>([])

  // 计算属性
  const logCount = computed(() => logs.value.length)

  const inboundLogs = computed(() => 
    logs.value.filter(log => log.type === '入库')
  )

  const outboundLogs = computed(() => 
    logs.value.filter(log => log.type === '出库')
  )

  const totalInboundQuantity = computed(() => 
    inboundLogs.value.reduce((sum, log) => sum + log.quantity, 0)
  )

  const totalOutboundQuantity = computed(() => 
    outboundLogs.value.reduce((sum, log) => sum + log.quantity, 0)
  )

  // 动作
  const addLog = (logData: OperationLog) => {
    // 将新日志添加到数组开头，保持最新的在前面
    logs.value.unshift({ ...logData })
  }

  const getLogsByRecord = (recordId: string): OperationLog[] => {
    return logs.value.filter(log => log.recordId === recordId)
  }

  const getLogsByType = (type: '入库' | '出库'): OperationLog[] => {
    return logs.value.filter(log => log.type === type)
  }

  const getLogsByDateRange = (startDate: string, endDate: string): OperationLog[] => {
    return logs.value.filter(log => {
      const logDate = new Date(log.timestamp)
      const start = new Date(startDate)
      const end = new Date(endDate)
      return logDate >= start && logDate <= end
    })
  }

  const clearLogs = () => {
    logs.value = []
  }

  const removeLog = (logId: string): boolean => {
    const index = logs.value.findIndex(log => log.id === logId)
    if (index > -1) {
      logs.value.splice(index, 1)
      return true
    }
    return false
  }

  // 获取最近的操作日志
  const getRecentLogs = (limit: number = 10): OperationLog[] => {
    return logs.value.slice(0, limit)
  }

  // 按产品统计操作次数
  const getOperationStatsByProduct = () => {
    const stats = new Map<string, { inbound: number, outbound: number, totalQuantity: number }>()
    
    logs.value.forEach(log => {
      if (!stats.has(log.productName)) {
        stats.set(log.productName, { inbound: 0, outbound: 0, totalQuantity: 0 })
      }
      
      const productStats = stats.get(log.productName)!
      if (log.type === '入库') {
        productStats.inbound += log.quantity
      } else {
        productStats.outbound += log.quantity
      }
      productStats.totalQuantity = productStats.inbound - productStats.outbound
    })
    
    return Array.from(stats.entries()).map(([productName, stats]) => ({
      productName,
      ...stats
    }))
  }

  return {
    // 状态
    logs,
    // 计算属性
    logCount,
    inboundLogs,
    outboundLogs,
    totalInboundQuantity,
    totalOutboundQuantity,
    // 动作
    addLog,
    getLogsByRecord,
    getLogsByType,
    getLogsByDateRange,
    clearLogs,
    removeLog,
    getRecentLogs,
    getOperationStatsByProduct
  }
})
