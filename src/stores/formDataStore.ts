import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ProductRecord, RecordData, StockOperation, StockOperationResult } from '@/types'
import { generateId, getCurrentTimestamp } from '@/utils'
import { useFormBuilderStore } from './formBuilderStore'
import { useLogStore } from './logStore'

export const useFormDataStore = defineStore('formData', () => {
  // 状态
  const records = ref<ProductRecord[]>([])

  // 计算属性
  const recordCount = computed(() => records.value.length)

  // 动作
  const addRecord = (recordData: RecordData): string => {
    const newRecord: ProductRecord = {
      recordId: generateId(),
      data: { ...recordData },
    }
    records.value.push(newRecord)
    return newRecord.recordId
  }

  const updateRecord = (recordId: string, newRecordData: RecordData): boolean => {
    const record = records.value.find((r) => r.recordId === recordId)
    if (record) {
      // 保留库存字段的值，只更新其他字段
      const formBuilderStore = useFormBuilderStore()
      const stockFieldId = formBuilderStore.stockField?.id

      const updatedData = { ...newRecordData }
      if (stockFieldId && record.data[stockFieldId] !== undefined) {
        updatedData[stockFieldId] = record.data[stockFieldId]
      }

      record.data = updatedData
      return true
    }
    return false
  }

  const deleteRecord = (recordId: string): boolean => {
    const index = records.value.findIndex((r) => r.recordId === recordId)
    if (index > -1) {
      records.value.splice(index, 1)
      return true
    }
    return false
  }

  const operateStock = (operation: StockOperation): StockOperationResult => {
    const formBuilderStore = useFormBuilderStore()
    const logStore = useLogStore()

    // 检查是否有库存字段
    const stockField = formBuilderStore.stockField
    if (!stockField) {
      return {
        success: false,
        message: '未设置库存字段',
      }
    }

    // 查找记录
    const record = records.value.find((r) => r.recordId === operation.recordId)
    if (!record) {
      return {
        success: false,
        message: '记录不存在',
      }
    }

    // 获取当前库存
    const currentStock = Number(record.data[stockField.id]) || 0
    const operationQuantity = Number(operation.quantity)

    // 验证操作数量
    if (isNaN(operationQuantity) || operationQuantity <= 0) {
      return {
        success: false,
        message: '操作数量必须为正数',
      }
    }

    let newStock: number
    let operationType: '入库' | '出库'

    // 计算新库存
    if (operation.type === 'inbound') {
      newStock = currentStock + operationQuantity
      operationType = '入库'
    } else {
      // 出库操作，检查库存是否足够
      if (currentStock < operationQuantity) {
        return {
          success: false,
          message: `库存不足，当前库存：${currentStock}`,
        }
      }
      newStock = currentStock - operationQuantity
      operationType = '出库'
    }

    // 更新库存
    record.data[stockField.id] = newStock

    // 获取产品名称（用于日志显示）
    const productNameField = formBuilderStore.fields.find(
      (f) => f.type === 'SingleLineText' && f.label.includes('名称'),
    )
    const productName = productNameField
      ? String(record.data[productNameField.id] || '未知产品')
      : '未知产品'

    // 记录操作日志
    logStore.addLog({
      id: generateId(),
      recordId: operation.recordId,
      productName,
      type: operationType,
      quantity: operationQuantity,
      stockBefore: currentStock,
      stockAfter: newStock,
      timestamp: getCurrentTimestamp(),
    })

    return {
      success: true,
      message: `${operationType}成功`,
      stockAfter: newStock,
    }
  }

  const getRecord = (recordId: string): ProductRecord | undefined => {
    return records.value.find((r) => r.recordId === recordId)
  }

  const getRecordsByField = (fieldId: string, value: any): ProductRecord[] => {
    return records.value.filter((r) => r.data[fieldId] === value)
  }

  // 重置所有记录
  const resetRecords = () => {
    records.value = []
  }

  return {
    // 状态
    records,
    // 计算属性
    recordCount,
    // 动作
    addRecord,
    updateRecord,
    deleteRecord,
    operateStock,
    getRecord,
    getRecordsByField,
    resetRecords,
  }
})
