import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { FieldDefinition, FieldConfig } from '@/types'
import { FieldType } from '@/types'
import { generateId } from '@/utils'

export const useFormBuilderStore = defineStore('formBuilder', () => {
  // 状态
  const fields = ref<FieldDefinition[]>([])

  // 计算属性
  const stockField = computed(() => {
    return fields.value.find((field) => field.config.isStockField === true)
  })

  const hasStockField = computed(() => {
    return !!stockField.value
  })

  // 动作
  const addField = (fieldType: FieldType) => {
    const newField: FieldDefinition = {
      id: generateId(),
      type: fieldType,
      label: getDefaultLabel(fieldType),
      config: {
        isRequired: false,
        isStockField: false,
      },
    }
    fields.value.push(newField)
    return newField.id
  }

  const removeField = (fieldId: string) => {
    const index = fields.value.findIndex((field) => field.id === fieldId)
    if (index > -1) {
      fields.value.splice(index, 1)
    }
  }

  const updateFieldConfig = (fieldId: string, newConfig: Partial<FieldConfig>) => {
    const field = fields.value.find((f) => f.id === fieldId)
    if (field) {
      field.config = { ...field.config, ...newConfig }
    }
  }

  const updateFieldLabel = (fieldId: string, newLabel: string) => {
    const field = fields.value.find((f) => f.id === fieldId)
    if (field) {
      field.label = newLabel
    }
  }

  const setStockField = (fieldId: string) => {
    // 首先将所有字段的 isStockField 设为 false
    fields.value.forEach((field) => {
      field.config.isStockField = false
    })

    // 然后将指定字段设为库存字段
    const targetField = fields.value.find((f) => f.id === fieldId)
    if (targetField) {
      targetField.config.isStockField = true
    }
  }

  const clearStockField = () => {
    fields.value.forEach((field) => {
      field.config.isStockField = false
    })
  }

  // 获取字段的默认标签
  const getDefaultLabel = (fieldType: FieldType): string => {
    const labelMap = {
      [FieldType.SINGLE_LINE_TEXT]: '单行文本',
      [FieldType.MULTI_LINE_TEXT]: '多行文本',
      [FieldType.NUMBER_INPUT]: '数字',
      [FieldType.DATE_PICKER]: '日期',
      [FieldType.SELECT]: '下拉选择',
      [FieldType.CHECKBOX]: '复选框',
      [FieldType.RADIO]: '单选框',
    }
    return labelMap[fieldType] || '未知字段'
  }

  // 重置所有字段
  const resetFields = () => {
    fields.value = []
  }

  return {
    // 状态
    fields,
    // 计算属性
    stockField,
    hasStockField,
    // 动作
    addField,
    removeField,
    updateFieldConfig,
    updateFieldLabel,
    setStockField,
    clearStockField,
    resetFields,
    getDefaultLabel,
  }
})
