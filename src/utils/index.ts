import { v4 as uuidv4 } from 'uuid'
import dayjs from 'dayjs'

/**
 * 生成唯一ID
 */
export const generateId = (): string => {
  return uuidv4()
}

/**
 * 格式化时间戳
 */
export const formatTimestamp = (timestamp?: string): string => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取当前时间戳
 */
export const getCurrentTimestamp = (): string => {
  return dayjs().toISOString()
}

/**
 * 深拷贝对象
 */
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj))
}

/**
 * 验证是否为有效数字
 */
export const isValidNumber = (value: any): boolean => {
  return !isNaN(value) && isFinite(value) && value >= 0
}
