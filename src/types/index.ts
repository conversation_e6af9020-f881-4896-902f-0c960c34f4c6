// 字段类型枚举
export enum FieldType {
  SINGLE_LINE_TEXT = 'SingleLineText',
  MULTI_LINE_TEXT = 'MultiLineText', 
  NUMBER_INPUT = 'NumberInput',
  DATE_PICKER = 'DatePicker',
  SELECT = 'Select',
  CHECKBOX = 'Checkbox',
  RADIO = 'Radio'
}

// 字段配置接口
export interface FieldConfig {
  isRequired?: boolean
  isStockField?: boolean
  placeholder?: string
  options?: string[] // 用于 Select, Radio, Checkbox
  min?: number // 用于 NumberInput
  max?: number // 用于 NumberInput
}

// 字段定义接口
export interface FieldDefinition {
  id: string
  type: FieldType
  label: string
  config: FieldConfig
}

// 记录数据接口
export interface RecordData {
  [fieldId: string]: any
}

// 产品记录接口
export interface ProductRecord {
  recordId: string
  data: RecordData
}

// 库存操作类型
export enum StockOperationType {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound'
}

// 库存操作参数接口
export interface StockOperation {
  recordId: string
  type: StockOperationType
  quantity: number
}

// 库存操作结果接口
export interface StockOperationResult {
  success: boolean
  message: string
  stockAfter?: number
}

// 操作日志接口
export interface OperationLog {
  id: string
  recordId: string
  productName: string
  type: '入库' | '出库'
  quantity: number
  stockBefore: number
  stockAfter: number
  timestamp: string
}
