<template>
  <div class="field-preview">
    <!-- 单行文本 -->
    <el-input 
      v-if="field.type === FieldType.SINGLE_LINE_TEXT"
      :placeholder="field.config.placeholder || '请输入' + field.label"
      disabled
    />
    
    <!-- 多行文本 -->
    <el-input 
      v-else-if="field.type === FieldType.MULTI_LINE_TEXT"
      type="textarea"
      :placeholder="field.config.placeholder || '请输入' + field.label"
      :rows="3"
      disabled
    />
    
    <!-- 数字输入 -->
    <el-input-number 
      v-else-if="field.type === FieldType.NUMBER_INPUT"
      :placeholder="field.config.placeholder || '请输入数字'"
      :min="field.config.min"
      :max="field.config.max"
      disabled
      style="width: 100%"
    />
    
    <!-- 日期选择 -->
    <el-date-picker 
      v-else-if="field.type === FieldType.DATE_PICKER"
      type="date"
      :placeholder="field.config.placeholder || '选择日期'"
      disabled
      style="width: 100%"
    />
    
    <!-- 下拉选择 -->
    <el-select 
      v-else-if="field.type === FieldType.SELECT"
      :placeholder="field.config.placeholder || '请选择'"
      disabled
      style="width: 100%"
    >
      <el-option 
        v-for="(option, index) in (field.config.options || ['选项1', '选项2', '选项3'])"
        :key="index"
        :label="option"
        :value="option"
      />
    </el-select>
    
    <!-- 复选框 -->
    <el-checkbox-group 
      v-else-if="field.type === FieldType.CHECKBOX"
      disabled
    >
      <el-checkbox 
        v-for="(option, index) in (field.config.options || ['选项1', '选项2', '选项3'])"
        :key="index"
        :label="option"
      >
        {{ option }}
      </el-checkbox>
    </el-checkbox-group>
    
    <!-- 单选框 -->
    <el-radio-group 
      v-else-if="field.type === FieldType.RADIO"
      disabled
    >
      <el-radio 
        v-for="(option, index) in (field.config.options || ['选项1', '选项2', '选项3'])"
        :key="index"
        :label="option"
      >
        {{ option }}
      </el-radio>
    </el-radio-group>
    
    <!-- 未知类型 -->
    <div v-else class="unknown-field">
      未知字段类型: {{ field.type }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  ElInput, 
  ElInputNumber, 
  ElDatePicker, 
  ElSelect, 
  ElOption,
  ElCheckboxGroup,
  ElCheckbox,
  ElRadioGroup,
  ElRadio
} from 'element-plus'
import type { FieldDefinition } from '@/types'
import { FieldType } from '@/types'

interface Props {
  field: FieldDefinition
}

defineProps<Props>()
</script>

<style scoped>
.field-preview {
  width: 100%;
}

.unknown-field {
  padding: 12px;
  background: #f5f5f5;
  border: 1px dashed #ccc;
  border-radius: 4px;
  color: #999;
  text-align: center;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
}

:deep(.el-textarea.is-disabled .el-textarea__inner) {
  background-color: #f5f7fa;
}

:deep(.el-input-number.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
}

:deep(.el-select.is-disabled .el-select__wrapper) {
  background-color: #f5f7fa;
}

:deep(.el-date-editor.is-disabled .el-input__wrapper) {
  background-color: #f5f7fa;
}
</style>
